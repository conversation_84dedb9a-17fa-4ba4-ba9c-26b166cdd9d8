import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON>alogHeader,
  DialogTitle,
} from "../../../components/ui/dialog";
import { Button } from "../../../components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../../components/ui/select";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "../../../components/ui/avatar";
import DefaultAvatar from "../../../assets/images/DefaultAvatar.png";
import { doctorSpecialties } from "../../../constants/doctorSpecialties";
import { ReactComponent as StethoscopeIcon } from "../../../assets/svgs/StethoscopeIcon.svg";
import { ReactComponent as DoctorIcon } from "../../../assets/svgs/DoctorIcon.svg";

const SharePatientEMRModal = ({ isOpen, onClose }) => {
  const [selectedSpecialty, setSelectedSpecialty] = useState("");
  const [selectedDoctor, setSelectedDoctor] = useState(null);

  // Hardcoded doctors for demonstration
  const hardcodedDoctors = [
    {
      id: 100,
      name: "Dr. Marcus Chen",
      specialization: "Cardiology",
      avatar: "https://github.com/shadcn.png",
      email: "<EMAIL>",
    },
    {
      id: 101,
      name: "Dr. Olivia Rodriguez",
      specialization: "Orthopedic",
      avatar: "https://github.com/shadcn.png",
      email: "<EMAIL>",
    },
    {
      id: 102,
      name: "Dr. William Panday",
      specialization: "ENT Specialist",
      avatar: "https://github.com/shadcn.png",
      email: "<EMAIL>",
    },
    {
      id: 103,
      name: "Dr. Isabella Kim",
      specialization: "Skin Specialist",
      avatar: "https://github.com/shadcn.png",
      email: "<EMAIL>",
    },
    {
      id: 104,
      name: "Dr. Noah Garcia",
      specialization: "Pediatrician",
      avatar: "https://github.com/shadcn.png",
      email: "<EMAIL>",
    },

    {
      id: 105,
      name: "Dr. William Panday",
      specialization: "Cardiology",
      avatar: "https://github.com/shadcn.png",
      email: "<EMAIL>",
    },
    {
      id: 106,
      name: "Dr. Isabella Kim",
      specialization: "Cardiology",
      avatar: "https://github.com/shadcn.png",
      email: "<EMAIL>",
    },
    {
      id: 107,
      name: "Dr. Noah Garcia",
      specialization: "Cardiology",
      avatar: "https://github.com/shadcn.png",
      email: "<EMAIL>",
    },

    {
      id: 108,
      name: "Dr. William Panday",
      specialization: "Cardiology",
      avatar: "https://github.com/shadcn.png",
      email: "<EMAIL>",
    },
    {
      id: 109,
      name: "Dr. Isabella Kim",
      specialization: "Cardiology",
      avatar: "https://github.com/shadcn.png",
      email: "<EMAIL>",
    },
    {
      id: 110,
      name: "Dr. Noah Garcia",
      specialization: "Cardiology",
      avatar: "https://github.com/shadcn.png",
      email: "<EMAIL>",
    },
  ];

  // Filter doctors based on selected specialty
  const filteredDoctors = selectedSpecialty
    ? hardcodedDoctors.filter(
        (doctor) =>
          doctor.specialization.toLowerCase() ===
          selectedSpecialty.toLowerCase(),
      )
    : [];

  // Handle share EMR
  const handleShareEMR = () => {
    resetAndClose();
  };

  // Reset state when modal closes
  const resetAndClose = () => {
    setSelectedSpecialty("");
    setSelectedDoctor(null);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={resetAndClose}>
      <DialogContent className="sm:max-w-[500px] p-0 bg-white rounded-2xl border-0 shadow-2xl">
        {/* Header */}
        <DialogHeader className="px-6 py-6">
          <DialogTitle className="text-2xl font-semibold text-[#1E1E1E]">
            Patient EMR
          </DialogTitle>
          <p className="text-gray-600 mt-2">
            Share Patient EMR Profile. For Internal data exchange only
          </p>
        </DialogHeader>

        {/* Content */}
        <div className="px-6 space-y-6">
          {/* Specialty Dropdown */}
          <Select
            value={selectedSpecialty}
            onValueChange={(value) => {
              setSelectedSpecialty(value);
              setSelectedDoctor(null);
            }}
          >
            <SelectTrigger>
              <div className="flex items-center gap-2">
                <StethoscopeIcon className="w-5 h-5" />
                <SelectValue placeholder="Select specialty" />
              </div>
            </SelectTrigger>
            <SelectContent>
              {doctorSpecialties.map((specialty, index) => (
                <SelectItem key={index} value={specialty.name}>
                  {specialty.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Doctor Dropdown */}
          <Select
            value={selectedDoctor ? selectedDoctor.id.toString() : ""}
            onValueChange={(value) => {
              const doctor = hardcodedDoctors.find(
                (d) => d.id.toString() === value,
              );
              setSelectedDoctor(doctor);
            }}
            disabled={!selectedSpecialty}
          >
            <SelectTrigger>
              <div className="flex items-center gap-2">
                <DoctorIcon className="w-5 h-5" />
                <SelectValue placeholder="Select Doctor" />
              </div>
            </SelectTrigger>
            <SelectContent
              className={
                filteredDoctors.length > 4 ? "max-h-64 overflow-y-auto" : ""
              }
            >
              {filteredDoctors.length > 0 ? (
                filteredDoctors.map((doctor) => (
                  <SelectItem
                    key={doctor.id}
                    value={doctor.id.toString()}
                    className="py-2"
                  >
                    <div className="flex items-center gap-2">
                      <Avatar className="w-7 h-7 border border-blue-600">
                        <AvatarImage
                          src={doctor.avatar || DefaultAvatar}
                          alt={doctor.name}
                        />
                        <AvatarFallback className="bg-blue-100 text-blue-600 text-xs">
                          {doctor.name
                            .split(" ")
                            .map((n) => n[0])
                            .join("")}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-normal text-start">{doctor.name}</p>
                        <p className="text-xs text-gray-500">{doctor.email}</p>
                      </div>
                    </div>
                  </SelectItem>
                ))
              ) : (
                <div className="py-2 px-2 text-gray-500 text-sm">
                  {selectedSpecialty
                    ? "No doctors found for this specialty"
                    : "Please select a specialty first"}
                </div>
              )}
            </SelectContent>
          </Select>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 space-y-3">
          <Button
            onClick={handleShareEMR}
            disabled={!selectedDoctor}
            className="w-full"
            variant={"primary"}
          >
            Share
          </Button>
          <Button variant="outline" onClick={resetAndClose} className="w-full">
            Cancel
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SharePatientEMRModal;
