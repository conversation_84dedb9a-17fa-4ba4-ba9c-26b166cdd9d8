import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from "../../../components/ui/tabs";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "../../../components/ui/avatar";
import { Button } from "../../../components/ui/button";
import { ReactComponent as ShareIcon } from "../../../assets/svgs/ShareBlue.svg";
import { Mail, Phone, MapPin } from "lucide-react";
import DefaultAvatar from "../../../assets/images/DefaultAvatar.png";
import { useLocation } from "react-router-dom";
import { fetchPatientById_api } from "../../../api/api_calls/patient_apiCalls";
import { getConcernedDoctors_api } from "../../../api/api_calls/emr_apiCalls";
import { toast } from "../../../hooks/use-toast";
import LoadingSpinner from "../../../components/animations/LoadingSpinner";
import SharedDoctorsModal from "../shared-doctors-modal/SharedDoctorsModal";
import SharePatientEMRModal from "../share-patient-emr-modal/SharePatientEMRModal";
import PatientInsuranceComponent from "../patient-insurance-component/PatientInsuranceComponent";
import PatientPreviousAppointments from "../patient-previous-appointments/PatientPreviousAppointments";

// Mock data for searchable doctors - replace with API call when ready
const searchableDoctorsData = [
  {
    id: 101,
    name: "Dr. Marcus Chen",
    specialization: "MBBS, MD (Cardiology)",
    avatar: "https://github.com/shadcn.png",
    email: "<EMAIL>",
    isShared: false,
  },
  {
    id: 102,
    name: "Dr. Olivia Rodriguez",
    specialization: "MBBS, MS (Orthopedics)",
    avatar: "https://github.com/shadcn.png",
    email: "<EMAIL>",
    isShared: false,
  },
  {
    id: 103,
    name: "Dr. Ethan Patel",
    specialization: "MBBS, MD (Neurology)",
    avatar: "https://github.com/shadcn.png",
    email: "<EMAIL>",
    isShared: false,
  },
  {
    id: 104,
    name: "Dr. Isabella Kim",
    specialization: "MBBS, MD (Dermatology)",
    avatar: "https://github.com/shadcn.png",
    email: "<EMAIL>",
    isShared: false,
  },
  {
    id: 105,
    name: "Dr. Noah Garcia",
    specialization: "MBBS, MD (Pediatrics)",
    avatar: "https://github.com/shadcn.png",
    email: "<EMAIL>",
    isShared: false,
  },
  {
    id: 106,
    name: "Dr. Ava Nguyen",
    specialization: "MBBS, MD (Psychiatry)",
    avatar: "https://github.com/shadcn.png",
    email: "<EMAIL>",
    isShared: false,
  },
  {
    id: 107,
    name: "Dr. Liam Wilson",
    specialization: "MBBS, MD (Gastroenterology)",
    avatar: "https://github.com/shadcn.png",
    email: "<EMAIL>",
    isShared: false,
  },
  {
    id: 108,
    name: "Dr. Mia Thompson",
    specialization: "MBBS, MD (Endocrinology)",
    avatar: "https://github.com/shadcn.png",
    email: "<EMAIL>",
    isShared: false,
  },
];

const PatientProfile = () => {
  const location = useLocation();
  const { state } = location;

  const [patient, setPatient] = useState(null);
  const [concernedDoctors, setConcernedDoctors] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isConcernedDoctorsLoading, setIsConcernedDoctorsLoading] =
    useState(false);

  const [showSharedDoctorsModal, setShowSharedDoctorsModal] = useState(false);
  const [showShareEMRModal, setShowShareEMRModal] = useState(false);
  const navigate = useNavigate();

  const viewDoctorProfile = (doctor) => {
    navigate("/doctor/view-doctor-profile", {
      state: {
        doctorData: doctor,
      },
    });
  };

  // Fetch patient profile from API
  const fetchPatientProfile = async () => {
    if (!state?.patientId) {
      toast({
        description: "Patient ID is required",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsLoading(true);
      const res = await fetchPatientById_api({
        patientId: state.patientId,
      });
      setPatient(res.data);
    } catch (error) {
      toast({
        description: error.message || "Failed to fetch patient profile",
        variant: "destructive",
      });
      console.log("ERROR IN fetchPatientProfile => ", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch concerned doctors from API
  const fetchConcernedDoctors = async () => {
    if (!state?.patientId) {
      return;
    }

    try {
      setIsConcernedDoctorsLoading(true);
      const res = await getConcernedDoctors_api({
        patientId: state.patientId,
      });
      setConcernedDoctors(res.data.doctors || []);
    } catch (error) {
      toast({
        description: error.message || "Failed to fetch concerned doctors",
        variant: "destructive",
      });
      console.log("ERROR IN fetchConcernedDoctors => ", error);
    } finally {
      setIsConcernedDoctorsLoading(false);
    }
  };

  // Fetch patient and concerned doctors on component mount
  useEffect(() => {
    fetchPatientProfile();
    fetchConcernedDoctors();
  }, [state?.patientId]); // eslint-disable-line react-hooks/exhaustive-deps

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <LoadingSpinner />
      </div>
    );
  }

  if (!patient) {
    return (
      <div className="p-4">
        <div className="py-8 text-center text-gray-500">
          Patient not found or failed to load.
        </div>
      </div>
    );
  }

  return (
    <div className="p-4">
      <div className="flex items-center flex-row justify-between">
        <h6 className="text-[#1E1E1E] font-bold text-[20px]">
          Patient Profile
        </h6>

        <div className="flex items-center flex-row gap-2">
          <div
            className="flex items-center gap-2 cursor-pointer hover:bg-gray-50 rounded-lg p-2 transition-colors"
            onClick={() => setShowSharedDoctorsModal(true)}
          >
            <h6 className="text-[14px] text-[#1E1E1E] hover:text-blue-600 transition-colors">
              {isConcernedDoctorsLoading
                ? "Loading..."
                : `Already shared with ${concernedDoctors.length} doctors`}
            </h6>
            <div className="items-center flex justify-center flex-row">
              {concernedDoctors.slice(0, 2).map((doctor, index) => (
                <Avatar
                  key={doctor.id}
                  className={`border-solid border-[#0052FD] border-[2px] ${index > 0 ? "-ml-5" : ""}`}
                >
                  <AvatarImage
                    src={doctor?.profilePicture?.url || DefaultAvatar}
                  />
                  <AvatarFallback>
                    {`${doctor?.firstName?.[0] || ""}${doctor?.lastName?.[0] || ""}`}
                  </AvatarFallback>
                </Avatar>
              ))}
              {concernedDoctors.length === 0 && !isConcernedDoctorsLoading && (
                <Avatar className="border-solid border-[#0052FD] border-[2px]">
                  <AvatarImage src={DefaultAvatar} />
                  <AvatarFallback>--</AvatarFallback>
                </Avatar>
              )}
            </div>
          </div>
          <Button
            variant={"secondary"}
            size={"lg"}
            onClick={() => setShowShareEMRModal(true)}
          >
            <ShareIcon /> Share Patient EMR
          </Button>
        </div>
      </div>

      <div className="bg-[#F8F8F8] rounded-xl p-6 w-full my-4">
        <div className="grid grid-cols-1  lg:grid-cols-2 xl:grid-cols-3  gap-6 items-center">
          <div className="flex items-center space-x-4">
            <Avatar className="w-20 h-20">
              <AvatarImage
                src={patient?.profilePicture?.url || DefaultAvatar}
                alt="Profile"
              />
              <AvatarFallback>
                {`${patient?.firstName?.[0] || ""}${patient?.lastName?.[0] || ""}`}
              </AvatarFallback>
            </Avatar>
            <div>
              <h2 className="text-xl font-semibold">
                {`${patient?.firstName || "N/A"} ${patient?.lastName || ""}`}
              </h2>
              <p className="text-gray-600">
                Gender: {patient?.gender || "N/A"}
              </p>
              <p className="text-gray-600">
                Date of Birth:{" "}
                {patient?.dob
                  ? new Date(patient.dob).toLocaleDateString()
                  : "N/A"}
              </p>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center text-gray-700">
              <Phone className="w-4 h-4 mr-2" />
              <span>{patient?.contactNumber || "N/A"}</span>
            </div>
            <div className="flex items-center text-gray-700">
              <Mail className="w-4 h-4 mr-2" />
              <span>{patient?.email || "N/A"}</span>
            </div>
            <div className="flex items-center text-gray-700">
              <MapPin className="w-4 h-4 mr-2" />
              <span>{patient?.address || "N/A"}</span>
            </div>
          </div>
          <div>
            <p className="text-gray-700">
              Country: {patient?.country || "N/A"}
            </p>
            <p className="text-gray-700">
              Medical Concerns:{" "}
              <span className="font-semibold">
                {patient?.medicalConcerns || "N/A"}
              </span>
            </p>
          </div>
        </div>
      </div>

      <Tabs defaultValue="insurance" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="insurance">Insurance</TabsTrigger>
          <TabsTrigger value="previous-appointment">
            Previous Appointment&apos;s
          </TabsTrigger>
        </TabsList>
        <TabsContent value="insurance">
          <PatientInsuranceComponent patient={patient} />
        </TabsContent>
        <TabsContent value="previous-appointment">
          <PatientPreviousAppointments patient={patient} />
        </TabsContent>
      </Tabs>

      {/* Shared Doctors Modal */}
      <SharedDoctorsModal
        isOpen={showSharedDoctorsModal}
        onClose={() => setShowSharedDoctorsModal(false)}
        doctors={concernedDoctors}
        viewDoctorProfile={viewDoctorProfile}
      />

      {/* Share Patient EMR Modal */}
      <SharePatientEMRModal
        isOpen={showShareEMRModal}
        onClose={() => setShowShareEMRModal(false)}
        doctors={searchableDoctorsData}
      />
    </div>
  );
};

export default PatientProfile;
